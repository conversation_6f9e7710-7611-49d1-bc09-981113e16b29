<?php

namespace Theme25\Frontend\Controller\Common;

class Header extends \Theme25\FrontendController {

    public function __construct($registry) {
        parent::__construct($registry, 'common/header');
    }

    public function index() {

        // Подготовка на данните с верижно извикване на методи
        $this->prepareBasicData()
             ->prepareNavigationData()
             ->prepareUserData()
             ->prepareMenuData();

        // Рендиране на шаблона с данните от $this->data
        $content = $this->loadView('common/header', $this->data);

        if(defined('STANDART_VIEW')) {
            return $content . '<div class="standart-view m-auto">';
        }
        
        return $content;
    }

    /**
     * Подготовка на основните данни за header-а
     */
    private function prepareBasicData() {
        // Logo и основни URL адреси
        $this->data['home_url'] = $this->getUrl('common/home');
        $this->data['logo_url'] = 'https://www.rakla.bg/image/rakla-logo.svg';
        $this->data['site_name'] = $this->getConfig('config_name', 'Ракла');

        return $this;
    }

    /**
     * Подготовка на данните за навигация
     */
    private function prepareNavigationData() {
        // Search functionality
        $this->data['search_placeholder'] = 'Търсене...';
        $this->data['ask_betty_text'] = 'Питай Бети';

        return $this;
    }

    /**
     * Подготовка на данните за потребителя
     */
    private function prepareUserData() {
        // User controls
        $this->data['wishlist_url'] = $this->getLink('account/wishlist');
        $this->data['account_url'] = $this->getLink('account/account');
        $this->data['cart_url'] = $this->getLink('checkout/cart');

        // Cart count - това трябва да се вземе от сесията или модел
        $this->data['cart_count'] = 0; // Временно статична стойност

        return $this;
    }

    private function prepareMenuData() {
        $this->data['menu_data'] = $this->loadController('common/mega_menu/getMegaMenuItems');
        return $this;
    }

}